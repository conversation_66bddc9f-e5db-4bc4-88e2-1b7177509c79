{
    "python.analysis.typeCheckingMode": "standard",
    "windsurfPyright.analysis.diagnosticMode": "workspace",
    "windsurfPyright.analysis.typeCheckingMode": "standard",
    // Enable JavaScript linting
    "eslint.enable": true,
    "eslint.validate": ["javascript", "javascriptreact"],
    // Set import root for JS/TS
    "javascript.preferences.importModuleSpecifier": "relative",
    "js/ts.implicitProjectConfig.checkJs": true,
    "jsconfig.paths": {
        "*": ["webui/*"]
    },
    // Optional: point VSCode to jsconfig.json if you add one
    "jsconfig.json": "${workspaceFolder}/jsconfig.json",
    "cursorpyright.analysis.typeCheckingMode": "standard",
    "cursor.general.enableModelSelection": true,
    "cursor.chat.useOpenAI": false,
    "cursor.chat.customProviders": [
      {
        "name": "LM Studio",
        "endpoint": "http://************:1234/v1",
        "apiKey": "lm-studio",
        "models": [
          "deepseek-coder-6.7b-instruct",
          "mistral-7b-instruct-v0.3-obliterated"
        ]
      }
    ],
    "cursor.cpp.codeActions": true,
    "cursor.cpp.enablePartialAccepts": true,


    "github.copilot.enable": {
        "*": true,
        "yaml": true,
        "plaintext": false,
        "markdown": false,
        "python": true,
        "javascript": true,
        "typescript": true,
        "json": true,
        "html": true,
        "css": true,
        "bash": true,
        "shell": true,
        "scminput": false
    },

    "github.copilot.advanced": {
        "length": 500,
        "temperature": 0.1,
        "top_p": 0.95,
        "inlineSuggestCount": 3
    },

    "github.copilot.editor.enableCodeActions": true,

    "github.copilot.chat.localeOverride": "en",
    "github.copilot.chat.welcomeMessage": "never",
    "github.copilot.renameSuggestions.triggerAutomatically": true,
    "github.copilot.conversation.additionalContext": true,

    "github.copilot.chat.experimental.codeGeneration.enable": true,
    "github.copilot.chat.experimental.codeGeneration.maxTokens": 1500,
    "github.copilot.chat.experimental.codeGeneration.temperature": 0.15,
    "github.copilot.chat.experimental.codeGeneration.topP": 0.9,
    "github.copilot.chat.experimental.codeGeneration.enableInlineSuggestions": true,
    "github.copilot.chat.experimental.codeGeneration.inlineSuggestionCount": 5,
    "github.copilot.chat.experimental.temporalContext": true,

    "editor.inlineSuggest.enabled": true,
    "editor.suggestSelection": "first",
    "editor.tabCompletion": "on",
    "editor.wordBasedSuggestions": "matchingDocuments",
    "editor.quickSuggestions": {
        "other": true,
        "comments": true,
        "strings": true
    },

    "python.defaultInterpreterPath": "./agent-zero-env/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,

    "files.associations": {
        "*.env": "dotenv",
        "*.md": "markdown",
        "Dockerfile*": "dockerfile",
        "*.yml": "yaml",
        "*.yaml": "yaml"
    },
    "github.copilot.chat.experimental.codeGeneration.instructions": [
      "Follow Python best practices and PEP 8",
      "Use type hints for all functions",
      "Implement proper error handling",
      "Add docstrings for all functions and classes",
      "Use async/await for I/O operations",
      "Follow clean code principles",
      "Implement proper logging",
      "Use meaningful variable and function names",
      "Implement proper exception handling",
      "Add comprehensive unit tests",
      "Use dependency injection where appropriate",
      "Follow SOLID principles"
    ]
}